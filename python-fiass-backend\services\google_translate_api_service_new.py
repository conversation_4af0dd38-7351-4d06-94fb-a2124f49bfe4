"""
Google Translate API Service using @vitalets/google-translate-api
This service provides high-quality translation for financial responses
with proper handling of capital words, special characters, and sentence integrity.
"""

import json
import subprocess
import os
import sys
import re
import hashlib
import time
import threading
from typing import Dict, Any, Optional, List, Tuple
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GoogleTranslateAPIService:
    """
    Google Translate API Service using @vitalets/google-translate-api
    Provides robust translation with better handling of:
    - Capital words and proper nouns
    - Special characters and formatting
    - Complete sentence integrity
    - Financial terminology
    """

    # Class-level rate limiting
    _last_request_time = 0
    _request_lock = threading.Lock()
    _min_request_interval = 1.0  # Minimum 1 second between requests

    def __init__(self):
        self.service_available = self._check_service_availability()
        self.node_script_path = self._setup_node_script()
        
        # Language mappings
        self.language_codes = {
            'Kannada': 'kn',
            'Tamil': 'ta',
            'Telugu': 'te', 
            'Oriya': 'or',
            'Odia': 'or',
            'English': 'en',
            'Hindi': 'hi'
        }
        
        self.language_names = {
            'kn': 'Kannada',
            'ta': 'Tamil',
            'te': 'Telugu',
            'or': 'Oriya', 
            'en': 'English',
            'hi': 'Hindi'
        }
        
    def _check_service_availability(self) -> bool:
        """Simple check if Node.js is available in PATH"""
        try:
            # Just try 'node' from PATH - simpler and works in virtual environments
            result = subprocess.run(['node', '--version'],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                self.node_executable = 'node'
                logger.info(f"✅ Found Node.js at: node")

                # Try npm from PATH
                try:
                    npm_result = subprocess.run(['npm', '--version'],
                                              capture_output=True, text=True, timeout=5)
                    if npm_result.returncode == 0:
                        self.npm_executable = 'npm'
                        logger.info(f"✅ Found npm at: npm")
                        return True
                except:
                    pass

                # Try npm.cmd for Windows
                try:
                    npm_cmd_result = subprocess.run(['npm.cmd', '--version'],
                                                  capture_output=True, text=True, timeout=5)
                    if npm_cmd_result.returncode == 0:
                        self.npm_executable = 'npm.cmd'
                        logger.info(f"✅ Found npm at: npm.cmd")
                        return True
                except:
                    pass

                logger.warning("npm not found in PATH")
                return False
            else:
                logger.warning("Node.js not found in PATH")
                return False

        except Exception as e:
            logger.error(f"Error checking Node.js availability: {e}")
            return False
    
    def _setup_node_script(self) -> Optional[str]:
        """Setup the Node.js translation script"""
        if not hasattr(self, 'service_available') or not self.service_available:
            return None
            
        try:
            # Create the services directory if it doesn't exist
            services_dir = os.path.dirname(os.path.abspath(__file__))
            script_path = os.path.join(services_dir, 'google_translate_script.js')
            
            # Create the Node.js script
            node_script = '''
const { translate } = require('@vitalets/google-translate-api');

// Function to identify capital words that should be preserved
function identifyCapitalWords(text) {
    const capitalWords = [];

    // Find acronyms (2+ consecutive capital letters)
    const acronyms = text.match(/\\b[A-Z]{2,}\\b/g) || [];
    capitalWords.push(...acronyms);

    // Find mixed-case proper nouns (like iPhone, SpaceX, GitHub)
    const mixedCase = text.match(/\\b[A-Z][a-z]*[A-Z][A-Za-z]*\\b/g) || [];
    capitalWords.push(...mixedCase);

    // Remove duplicates
    const uniqueWords = [...new Set(capitalWords)];

    if (uniqueWords.length > 0) {
        console.error(`🔍 Identified capital words to preserve: ${uniqueWords.join(', ')}`);
    }

    return uniqueWords;
}

// Function to preserve capital words in translation
function preserveCapitalWords(originalText, translatedText, capitalWords) {
    if (!capitalWords || capitalWords.length === 0) {
        return translatedText;
    }

    let preservedText = translatedText;

    console.error(`🔧 Preserving ${capitalWords.length} capital words in translation`);

    for (const word of capitalWords) {
        // Check if the word is already present in the translation
        if (preservedText.includes(word)) {
            console.error(`✅ Capital word '${word}' already preserved in translation`);
            continue;
        }

        // Strategy: Find the position of the word in the original text and try to preserve it
        const wordRegex = new RegExp(`\\\\b${word}\\\\b`, 'gi');
        const originalMatches = originalText.match(wordRegex);

        if (originalMatches) {
            console.error(`🔄 Restoring missing capital word '${word}' in translation`);

            // Simple approach: if the word is missing, try to find a good place to insert it
            // This could be enhanced with more sophisticated word alignment

            // For technical terms and proper nouns, we want to keep them as-is
            // Look for potential translated versions and replace them

            // Common patterns where capital words might get corrupted:
            // 1. Completely missing
            // 2. Translated to local script
            // 3. Broken into parts

            // For now, we'll use a simple strategy of ensuring the word appears
            // in the translation by replacing similar-length words or adding it

            // If the translation is significantly shorter, the word might be missing
            if (translatedText.length < originalText.length * 0.7) {
                // Add the word at the beginning or end contextually
                preservedText = `${word} ${preservedText}`;
                console.error(`🔄 Added missing capital word '${word}' to translation`);
            } else {
                // Try to find and replace potential translations
                // This is a basic implementation - could be much more sophisticated
                const words = preservedText.split(' ');
                let inserted = false;

                // Look for a good position to insert the word
                for (let i = 0; i < words.length; i++) {
                    // If we find a word that might be a translation of our capital word
                    // (similar length, position), replace it
                    if (words[i].length >= word.length - 2 && words[i].length <= word.length + 2) {
                        words[i] = word;
                        preservedText = words.join(' ');
                        inserted = true;
                        console.error(`🔄 Replaced potential translation with '${word}'`);
                        break;
                    }
                }

                if (!inserted) {
                    // As a fallback, add the word at the end
                    preservedText = `${preservedText} ${word}`;
                    console.error(`🔄 Added '${word}' at the end of translation`);
                }
            }
        }
    }

    return preservedText;
}

async function translateText(text, targetLang, sourceLang = 'auto') {
    try {
        // Identify capital words before translation
        const capitalWords = identifyCapitalWords(text);

        const result = await translate(text, {
            from: sourceLang,
            to: targetLang,
            fetchOptions: {
                agent: null // Disable proxy for better reliability
            }
        });

        // Handle different response formats
        let translatedText = result.text || result;
        let detectedLanguage = 'auto';
        let confidence = 0.95;

        // Try to extract language info if available
        if (result.from && result.from.language) {
            detectedLanguage = result.from.language.iso || result.from.language;
            confidence = result.from.language.didYouMean ? 0.8 : 0.95;
        } else if (typeof result === 'string') {
            translatedText = result;
        }

        // Preserve capital words in the translation
        const preservedTranslation = preserveCapitalWords(text, translatedText, capitalWords);

        return {
            success: true,
            translatedText: preservedTranslation,
            detectedLanguage: detectedLanguage,
            confidence: confidence,
            originalText: text,
            capitalWordsPreserved: capitalWords
        };
    } catch (error) {
        return {
            success: false,
            error: error.message,
            originalText: text
        };
    }
}

async function translateFinancialResponse(responseData, targetLang) {
    try {
        const results = {};

        // Translate AI response with capital word preservation and reference number injection
        if (responseData.ai_response) {
            console.error(`🔄 Translating AI response with capital word preservation and reference injection`);

            // First, inject reference numbers into the AI response if sentence analysis is available
            let responseToTranslate = responseData.ai_response;
            if (responseData.sentence_analysis && Array.isArray(responseData.sentence_analysis)) {
                responseToTranslate = injectReferenceNumbers(responseData.ai_response, responseData.sentence_analysis);
                console.error(`🔢 Reference numbers injected into AI response`);
            }

            const aiResult = await translateText(responseToTranslate, targetLang);
            if (aiResult.success) {
                results.ai_response = aiResult.translatedText;
                results.ai_response_capital_words = aiResult.capitalWordsPreserved || [];
            } else {
                results.ai_response_error = aiResult.error;
            }
        }

        // Translate related questions with capital word preservation
        if (responseData.related_questions && Array.isArray(responseData.related_questions)) {
            results.related_questions = [];
            results.related_questions_capital_words = [];

            for (const question of responseData.related_questions) {
                if (question && question.trim()) {
                    console.error(`🔄 Translating related question: ${question.substring(0, 50)}...`);
                    const qResult = await translateText(question, targetLang);
                    if (qResult.success) {
                        results.related_questions.push(qResult.translatedText);
                        if (qResult.capitalWordsPreserved) {
                            results.related_questions_capital_words.push(qResult.capitalWordsPreserved);
                        }
                    } else {
                        results.related_questions.push(question); // Keep original if translation fails
                        results.related_questions_capital_words.push([]);
                    }
                } else {
                    results.related_questions.push(question);
                    results.related_questions_capital_words.push([]);
                }
            }
        }

        return {
            success: true,
            data: results
        };
    } catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
}

// Function to inject reference numbers into AI response text
function injectReferenceNumbers(aiResponse, sentenceAnalysis) {
    try {
        let modifiedResponse = aiResponse;

        // Create a map of sentences to reference numbers
        const sentenceToRefMap = new Map();
        sentenceAnalysis.forEach((item, index) => {
            if (item.sentence && item.sentence.trim()) {
                sentenceToRefMap.set(item.sentence.trim(), index + 1);
            }
        });

        // Try to find and inject reference numbers
        sentenceAnalysis.forEach((item, index) => {
            if (item.sentence && item.sentence.trim()) {
                const sentence = item.sentence.trim();
                const refNumber = index + 1;

                // Look for the sentence in the AI response
                const sentenceIndex = modifiedResponse.indexOf(sentence);
                if (sentenceIndex !== -1) {
                    // Insert reference number after the sentence
                    const beforeSentence = modifiedResponse.substring(0, sentenceIndex + sentence.length);
                    const afterSentence = modifiedResponse.substring(sentenceIndex + sentence.length);

                    // Add reference number with space handling
                    const referenceTag = ` [${refNumber}]`;
                    modifiedResponse = beforeSentence + referenceTag + afterSentence;

                    console.error(`🔢 Added reference [${refNumber}] after sentence: "${sentence.substring(0, 50)}..."`);
                }
            }
        });

        // If no direct sentence matches found, add references at the end of paragraphs
        if (modifiedResponse === aiResponse && sentenceAnalysis.length > 0) {
            console.error(`🔢 No direct sentence matches found, adding references at paragraph ends`);

            // Split into paragraphs and add references
            const paragraphs = modifiedResponse.split('\n\n');
            const modifiedParagraphs = paragraphs.map((paragraph, pIndex) => {
                if (paragraph.trim() && pIndex < sentenceAnalysis.length) {
                    const refNumbers = sentenceAnalysis.slice(0, Math.min(3, sentenceAnalysis.length))
                        .map((_, index) => `[${index + 1}]`)
                        .join(' ');
                    return paragraph.trim() + ' ' + refNumbers;
                }
                return paragraph;
            });

            modifiedResponse = modifiedParagraphs.join('\n\n');
        }

        return modifiedResponse;
    } catch (error) {
        console.error(`❌ Error injecting reference numbers: ${error.message}`);
        return aiResponse; // Return original if injection fails
    }
}

// Main execution
async function main() {
    try {
        const args = process.argv.slice(2);
        const command = args[0];
        
        if (command === 'translate') {
            const text = args[1];
            const targetLang = args[2];
            const sourceLang = args[3] || 'auto';
            
            const result = await translateText(text, targetLang, sourceLang);
            console.log(JSON.stringify(result));
            
        } else if (command === 'translate-response') {
            const responseDataStr = args[1];
            const targetLang = args[2];
            
            const responseData = JSON.parse(responseDataStr);
            const result = await translateFinancialResponse(responseData, targetLang);
            console.log(JSON.stringify(result));
            
        } else {
            console.log(JSON.stringify({
                success: false,
                error: 'Invalid command. Use "translate" or "translate-response"'
            }));
        }
    } catch (error) {
        console.log(JSON.stringify({
            success: false,
            error: error.message
        }));
    }
}

main();
'''
            
            # Write the script
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(node_script)
            
            # Install the required package if not already installed
            self._install_translate_package(services_dir)
            
            logger.info(f"✅ Node.js translation script created at: {script_path}")
            return script_path
            
        except Exception as e:
            logger.error(f"Error setting up Node.js script: {e}")
            return None
    
    def _install_translate_package(self, services_dir: str):
        """Install @vitalets/google-translate-api package"""
        try:
            # Check if package.json exists
            package_json_path = os.path.join(services_dir, 'package.json')
            if not os.path.exists(package_json_path):
                # Create package.json
                package_json = {
                    "name": "google-translate-service",
                    "version": "1.0.0",
                    "description": "Google Translate API service for financial responses",
                    "dependencies": {
                        "@vitalets/google-translate-api": "^9.2.0"
                    }
                }
                
                with open(package_json_path, 'w') as f:
                    json.dump(package_json, f, indent=2)
                
                logger.info("📦 Created package.json")
            
            # Install the package
            npm_cmd = getattr(self, 'npm_executable', 'npm')
            result = subprocess.run([npm_cmd, 'install'],
                                  cwd=services_dir,
                                  capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                logger.info("✅ @vitalets/google-translate-api installed successfully")
            else:
                logger.warning(f"⚠️ npm install warning: {result.stderr}")
                
        except Exception as e:
            logger.error(f"Error installing translate package: {e}")
    
    def is_service_available(self) -> bool:
        """Check if the translation service is available"""
        return self.service_available

    def _apply_rate_limiting(self):
        """Apply rate limiting to prevent hitting API limits"""
        with self._request_lock:
            current_time = time.time()
            time_since_last_request = current_time - self._last_request_time

            if time_since_last_request < self._min_request_interval:
                sleep_time = self._min_request_interval - time_since_last_request
                logger.info(f"⏱️ Rate limiting: sleeping for {sleep_time:.2f} seconds")
                time.sleep(sleep_time)

            self._last_request_time = time.time() and self.node_script_path is not None

    def _identify_capital_words(self, text: str) -> List[str]:
        """
        Identify capital words that should be preserved during translation.

        Args:
            text: Input text to analyze

        Returns:
            List of capital words to preserve
        """
        capital_words = []

        # Find acronyms (2+ consecutive capital letters)
        acronyms = re.findall(r'\b[A-Z]{2,}\b', text)
        capital_words.extend(acronyms)

        # Find mixed-case proper nouns (like iPhone, SpaceX, GitHub)
        mixed_case = re.findall(r'\b[A-Z][a-z]*[A-Z][A-Za-z]*\b', text)
        capital_words.extend(mixed_case)

        # Remove duplicates while preserving order
        seen = set()
        unique_capital_words = []
        for word in capital_words:
            if word not in seen:
                seen.add(word)
                unique_capital_words.append(word)

        if unique_capital_words:
            logger.info(f"🔍 Identified capital words to preserve: {unique_capital_words}")

        return unique_capital_words

    def _preserve_capital_words_in_translation(self, original_text: str, translated_text: str, capital_words: List[str]) -> str:
        """
        Restore capital words in their original English form within translated text.

        Args:
            original_text: Original English text
            translated_text: Translated text from Google Translate
            capital_words: List of capital words to preserve

        Returns:
            Translated text with capital words preserved in English
        """
        if not capital_words:
            return translated_text

        preserved_text = translated_text

        logger.info(f"🔧 Preserving {len(capital_words)} capital words in translation")

        for word in capital_words:
            # Check if the original word is still present
            if word in preserved_text:
                logger.info(f"✅ Capital word '{word}' already preserved in translation")
                continue

            logger.info(f"🔄 Restoring missing capital word '{word}' in translation")

            # Strategy 1: Look for potential corrupted versions of the word
            # and replace them with the original

            # Strategy 2: If the word is completely missing, find a good place to insert it
            # based on the original text structure

            # Find the position of the word in the original text
            word_positions = [m.start() for m in re.finditer(re.escape(word), original_text)]

            if word_positions:
                # Calculate relative position (beginning, middle, end)
                text_length = len(original_text)
                relative_position = word_positions[0] / text_length

                # Insert the word at a similar relative position in the translation
                translated_length = len(preserved_text)
                insert_position = int(relative_position * translated_length)

                # Find the nearest word boundary
                words = preserved_text.split()
                if words:
                    # Calculate which word index to insert at
                    char_count = 0
                    insert_word_index = 0

                    for i, w in enumerate(words):
                        char_count += len(w) + 1  # +1 for space
                        if char_count >= insert_position:
                            insert_word_index = i
                            break

                    # Insert the capital word at the calculated position
                    words.insert(insert_word_index, word)
                    preserved_text = ' '.join(words)
                    logger.info(f"🔄 Inserted '{word}' at position {insert_word_index} in translation")
                else:
                    # If no words, just add the word
                    preserved_text = word
                    logger.info(f"🔄 Added '{word}' to empty translation")
            else:
                # Fallback: add at the beginning
                preserved_text = f"{word} {preserved_text}"
                logger.info(f"🔄 Added '{word}' at the beginning of translation")

        logger.info(f"📝 Final preserved text: {preserved_text[:200]}...")
        return preserved_text

    def _preserve_capital_words(self, text: str) -> Tuple[str, List[Tuple[str, str]]]:
        """
        Extract and preserve words with capital letters from text using CAPWORD placeholders.

        Args:
            text: Input text

        Returns:
            tuple: (modified_text_with_placeholders, capital_words_list)
        """
        # Find all words with continuous capital letters (2+ consecutive capitals) or important mixed-case words
        continuous_capital_matches = re.findall(r'\b[A-Z]{2,}\b', text)  # Pure capitals like NASA, API
        mixed_case_matches = re.findall(r'\b[A-Z][a-z]*[A-Z][A-Za-z]*\b', text)  # Mixed case like SpaceX, iPhone

        # Combine both patterns and remove duplicates
        all_matches = list(set(continuous_capital_matches + mixed_case_matches))
        capital_words = []
        modified_text = text

        logger.info(f"🔍 Found {len(all_matches)} capital words to preserve: {all_matches}")

        # Create robust placeholders using word-based tokens
        for i, word in enumerate(all_matches):
            # Create a unique but translation-resistant placeholder
            word_hash = hashlib.md5(word.encode()).hexdigest()[:6]
            placeholder = f"CAPWORD{i}_{word_hash}_CAPWORD"
            capital_words.append((word, placeholder))

            # Replace only the first occurrence to avoid issues with repeated words
            modified_text = modified_text.replace(word, placeholder, 1)
            logger.info(f"🔧 Replaced '{word}' with '{placeholder}'")

        logger.info(f"📝 Modified text preview: {modified_text[:100]}...")
        return modified_text, capital_words

    def _restore_capital_words(self, text: str, capital_words: List[Tuple[str, str]]) -> str:
        """
        Restore capital words from placeholders with enhanced error handling.

        Args:
            text: Text with placeholders
            capital_words: List of (word, placeholder) tuples

        Returns:
            Text with capital words restored
        """
        restored_text = text

        logger.info(f"🔧 Restoring {len(capital_words)} capital words from placeholders")
        logger.info(f"📝 Text before restoration: {restored_text[:200]}...")

        for word, placeholder in capital_words:
            logger.info(f"🔍 Looking for placeholder '{placeholder}' to restore '{word}'")

            # Try exact match first
            if placeholder in restored_text:
                restored_text = restored_text.replace(placeholder, word)
                logger.info(f"✅ Exact match restored: {placeholder} -> {word}")
            else:
                # Try pattern match for partially modified placeholders
                patterns_to_try = [
                    # Try with spaces around the placeholder
                    placeholder.replace('CAPWORD', r'CAPWORD\s*'),
                    # Try with partial matches
                    re.escape(placeholder).replace(r'CAPWORD', r'CAPWORD\s*'),
                    # Try to find the core pattern with case insensitive matching
                    f"CAPWORD{placeholder.split('_')[0][-1]}.*CAPWORD" if '_' in placeholder else placeholder,
                    # Case insensitive version of the placeholder
                    re.escape(placeholder).replace(r'CAPWORD', r'(?i)CAPWORD'),
                ]

                restored = False
                for pattern in patterns_to_try:
                    try:
                        if re.search(pattern, restored_text):
                            restored_text = re.sub(pattern, word, restored_text, count=1)
                            logger.info(f"🔄 Pattern match restored: {pattern} -> {word}")
                            restored = True
                            break
                    except re.error:
                        continue

                if not restored:
                    logger.warning(f"⚠️ Could not restore placeholder: {placeholder} for word: {word}")
                    # As a last resort, try to find any CAPWORD pattern that might be our placeholder
                    capword_patterns = [
                        r'CAPWORD\d+_[a-f0-9]+_CAPWORD',  # Exact pattern
                        r'(?i)CAPWORD\d+_[a-f0-9]+_CAPWORD',  # Case insensitive
                        r'CAPWORD\d+.*CAPWORD',  # Very flexible
                        r'(?i)CAPWORD\d+.*CAPWORD'  # Very flexible case insensitive
                    ]

                    for fallback_pattern in capword_patterns:
                        try:
                            if re.search(fallback_pattern, restored_text):
                                # Replace the first occurrence of any CAPWORD pattern
                                restored_text = re.sub(fallback_pattern, word, restored_text, count=1)
                                logger.info(f"🔄 Fallback restoration applied for: {word} using pattern: {fallback_pattern}")
                                restored = True
                                break
                        except re.error:
                            continue

        logger.info(f"📝 Text after restoration: {restored_text[:200]}...")
        return restored_text
    
    def translate_text(self, text: str, target_lang: str, source_lang: str = 'auto') -> Dict[str, Any]:
        """
        Translate text using Google Translate API with capital word preservation

        Args:
            text: Text to translate
            target_lang: Target language code (e.g., 'kn', 'ta', 'te', 'or')
            source_lang: Source language code (default: 'auto')

        Returns:
            Dictionary with translation result
        """
        if not self.is_service_available():
            return {
                'success': False,
                'error': 'Translation service not available'
            }

        try:
            # Apply rate limiting to prevent hitting API limits
            self._apply_rate_limiting()

            # Identify capital words before translation
            capital_words = self._identify_capital_words(text)

            # Run the Node.js script (which now handles capital word preservation internally)
            node_cmd = getattr(self, 'node_executable', 'node')
            result = subprocess.run([
                node_cmd, self.node_script_path, 'translate',
                text, target_lang, source_lang
            ], capture_output=True, text=True, encoding='utf-8', timeout=30)

            if result.returncode == 0:
                node_response = json.loads(result.stdout)

                # Check if the Node.js script succeeded
                if node_response.get('success'):
                    translated_text = node_response.get('translatedText', text)

                    # Additional Python-side capital word preservation if needed
                    if capital_words:
                        preserved_text = self._preserve_capital_words_in_translation(
                            text, translated_text, capital_words
                        )
                        translated_text = preserved_text

                    # Return in the format expected by the Python service
                    return {
                        'success': True,
                        'translated_text': translated_text,
                        'original_text': text,
                        'source_language': node_response.get('detectedLanguage', source_lang),
                        'target_language': target_lang,
                        'translation_provider': 'google_translate_api_new',
                        'capital_words_preserved': capital_words
                    }
                else:
                    # Node.js script failed (e.g., rate limit, network error)
                    error_msg = node_response.get('error', 'Unknown translation error')
                    logger.warning(f"Node.js translation failed: {error_msg}")

                    # Try fallback to old translation service (without CAPWORD tokens)
                    try:
                        from services.translation_service import TranslationService
                        fallback_service = TranslationService()

                        logger.info("🔄 Attempting fallback to old translation service...")
                        fallback_result = fallback_service.translate_text(text, target_lang, source_lang)

                        logger.info(f"🔍 Fallback result: {fallback_result}")

                        # Check if fallback was successful (old service doesn't return 'success' field)
                        fallback_translated = fallback_result.get('translated_text', '')
                        if fallback_translated and fallback_translated != text and not fallback_result.get('error'):
                            logger.info("✅ Fallback translation successful")
                            # Preserve capital words in fallback translation
                            translated_text = fallback_translated
                            if capital_words:
                                translated_text = self._preserve_capital_words_in_translation(
                                    text, translated_text, capital_words
                                )

                            return {
                                'success': True,
                                'translated_text': translated_text,
                                'original_text': text,
                                'source_language': fallback_result.get('source_language', source_lang),
                                'target_language': target_lang,
                                'translation_provider': 'fallback_service',
                                'capital_words_preserved': capital_words,
                                'fallback_used': True
                            }
                        else:
                            logger.warning(f"❌ Fallback translation failed: {fallback_result.get('error', 'No translation returned')}")

                    except Exception as fallback_error:
                        logger.error(f"❌ Fallback service error: {fallback_error}")

                    # If all translation attempts fail, return original text
                    return {
                        'success': False,
                        'error': error_msg,
                        'original_text': text,
                        'translated_text': text,  # Return original text as final fallback
                        'source_language': source_lang,
                        'target_language': target_lang,
                        'translation_provider': 'google_translate_api_new'
                    }
            else:
                logger.error(f"Translation script error: {result.stderr}")
                return {
                    'success': False,
                    'error': f'Translation script failed: {result.stderr}'
                }

        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': 'Translation request timed out'
            }
        except json.JSONDecodeError as e:
            return {
                'success': False,
                'error': f'Failed to parse translation response: {e}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'Translation error: {str(e)}'
            }
    
    def translate_financial_response(self, response_data: Dict[str, Any], target_lang: str) -> Dict[str, Any]:
        """
        Translate entire financial response including AI response and related questions
        
        Args:
            response_data: The complete financial response data
            target_lang: Target language code
            
        Returns:
            Dictionary with translated response data
        """
        if not self.is_service_available():
            return {
                'success': False,
                'error': 'Translation service not available'
            }
        
        try:
            # Prepare data for translation including sentence analysis for reference injection
            translation_data = {
                'ai_response': response_data.get('ai_response', ''),
                'related_questions': response_data.get('related_questions', []),
                'sentence_analysis': response_data.get('sentence_analysis', [])  # Include sentence analysis for reference injection
            }

            # Convert to JSON string for command line
            data_json = json.dumps(translation_data)
            
            # Run the Node.js script
            node_cmd = getattr(self, 'node_executable', 'node')
            result = subprocess.run([
                node_cmd, self.node_script_path, 'translate-response',
                data_json, target_lang
            ], capture_output=True, text=True, encoding='utf-8', timeout=60)
            
            if result.returncode == 0:
                translation_result = json.loads(result.stdout)
                
                if translation_result.get('success'):
                    # Update the original response data with translations
                    updated_response = response_data.copy()
                    translated_data = translation_result['data']

                    if 'ai_response' in translated_data:
                        updated_response['ai_response'] = translated_data['ai_response']

                    if 'related_questions' in translated_data:
                        updated_response['related_questions'] = translated_data['related_questions']

                    # Add translation metadata
                    if 'query_metadata' not in updated_response:
                        updated_response['query_metadata'] = {}

                    updated_response['query_metadata']['response_translated'] = True
                    updated_response['query_metadata']['translation_service'] = '@vitalets/google-translate-api'
                    updated_response['query_metadata']['target_language'] = target_lang

                    # Add capital word preservation metadata
                    if 'ai_response_capital_words' in translated_data:
                        updated_response['query_metadata']['ai_response_capital_words_preserved'] = translated_data['ai_response_capital_words']

                    if 'related_questions_capital_words' in translated_data:
                        updated_response['query_metadata']['related_questions_capital_words_preserved'] = translated_data['related_questions_capital_words']

                    logger.info("✅ Translation completed with capital word preservation")

                    return {
                        'success': True,
                        'response': updated_response,  # Use 'response' key to match expected structure
                        'data': updated_response
                    }
                else:
                    return translation_result
            else:
                logger.error(f"Translation script error: {result.stderr}")
                return {
                    'success': False,
                    'error': f'Translation script failed: {result.stderr}'
                }
                
        except subprocess.TimeoutExpired:
            return {
                'success': False,
                'error': 'Translation request timed out'
            }
        except json.JSONDecodeError as e:
            return {
                'success': False,
                'error': f'Failed to parse translation response: {e}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'Translation error: {str(e)}'
            }
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported language codes"""
        return [
            'en',  # English
            'kn',  # Kannada
            'ta',  # Tamil
            'te',  # Telugu
            'or',  # Oriya
            'hi',  # Hindi
            'bn',  # Bengali
            'gu',  # Gujarati
            'ml',  # Malayalam
            'mr',  # Marathi
            'pa',  # Punjabi
            'ur',  # Urdu
        ]

# Create a global instance
google_translate_service = GoogleTranslateAPIService()

# Export for easy importing
__all__ = ['google_translate_service', 'GoogleTranslateAPIService']